@tailwind base;
@tailwind components;
@tailwind utilities;

/* Tailwind CSS Optimization Layer */
@layer base {
  /* Ensure Tailwind base styles are always applied */
  html {
    -webkit-text-size-adjust: 100%;
    font-feature-settings: normal;
    font-variation-settings: normal;
    tab-size: 4;
  }

  body {
    margin: 0;
    line-height: inherit;
  }

  /* Prevent FOUC (Flash of Unstyled Content) */
  .tailwind-loading {
    visibility: hidden;
  }

  .tailwind-loaded {
    visibility: visible;
  }
}

@layer components {
  /* Force important utility classes to prevent disconnection */
  .force-flex { @apply flex !important; }
  .force-grid { @apply grid !important; }
  .force-hidden { @apply hidden !important; }
  .force-block { @apply block !important; }
  .force-inline-block { @apply inline-block !important; }

  /* Ensure critical layout classes are never purged */
  .layout-container { @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8; }
  .layout-grid { @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6; }
  .layout-flex { @apply flex items-center justify-between; }
}

:root {
  --background: #ffffff;
  --foreground: #171717;
  --font-geist-sans: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-geist-mono: Monaco, Consolas, monospace;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Prevent FOUC and layout shifts */
.dashboard-banner,
[class*="dashboard-banner"] {
  border-radius: 1rem;
  background: linear-gradient(to right, #0c035f, #4f46e5);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  padding: 2rem;
  color: white;
  transition: none;
  transform: none;
  /* Prevent layout shifts during initial render */
  contain: layout style;
  content-visibility: auto;
}

/* Custom form styles with improved contrast */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="date"],
input[type="search"],
textarea,
select {
  @apply border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #ffffff;
  color: #1f2937; /* Dark gray text for better contrast */
}

/* Improved placeholder styling for better visibility */
input[type="text"]::placeholder,
input[type="email"]::placeholder,
input[type="password"]::placeholder,
input[type="date"]::placeholder,
input[type="search"]::placeholder,
textarea::placeholder,
select::placeholder {
  color: #6b7280; /* Medium gray for better contrast than light gray */
  opacity: 1; /* Ensure full opacity for better visibility */
}

/* Focus states with better contrast */
input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="date"]:focus,
input[type="search"]:focus,
textarea:focus,
select:focus {
  background-color: #ffffff;
  color: #111827; /* Even darker text on focus */
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Disabled state styling */
input[type="text"]:disabled,
input[type="email"]:disabled,
input[type="password"]:disabled,
input[type="date"]:disabled,
input[type="search"]:disabled,
textarea:disabled,
select:disabled {
  background-color: #f9fafb;
  color: #6b7280;
  cursor: not-allowed;
}

input[type="text"]:disabled::placeholder,
input[type="email"]:disabled::placeholder,
input[type="password"]:disabled::placeholder,
input[type="date"]:disabled::placeholder,
input[type="search"]:disabled::placeholder,
textarea:disabled::placeholder,
select:disabled::placeholder {
  color: #9ca3af;
}

/* Utility classes for consistent input styling */
.input-primary {
  @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900 placeholder-gray-600 transition-colors;
}

.input-search {
  @apply w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900 placeholder-gray-600 transition-colors;
}

.input-chat {
  @apply flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-900 placeholder-gray-600 transition-colors;
}

/* Select dropdown styling */
select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

/* Improved focus ring for better accessibility */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

/* Error state styling */
.input-error {
  @apply border-red-300 focus:border-red-500 focus:ring-red-500;
}

/* Success state styling */
.input-success {
  @apply border-green-300 focus:border-green-500 focus:ring-green-500;
}

/* Professional gradient backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Status badge styles */
.status-active {
  background-color: #d4edda;
  color: #155724;
  border-color: #c3e6cb;
}

.status-inactive {
  background-color: #f8d7da;
  color: #721c24;
  border-color: #f5c6cb;
}

.status-on-leave {
  background-color: #fff3cd;
  color: #856404;
  border-color: #ffeaa7;
}

.status-suspended {
  background-color: #f5c6cb;
  color: #721c24;
  border-color: #f1b0b7;
}

/* Professional typography */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
}

/* Smooth transitions - Applied selectively to avoid layout shifts */
button,
a,
input,
textarea,
select,
.transition-element {
  transition: all 0.2s ease-in-out;
}

/* Hover transitions for interactive elements */
button:hover,
a:hover,
.hover-transition:hover {
  transition: all 0.2s ease-in-out;
}

/* Prevent layout shifts on dashboard banner */
.dashboard-banner {
  border-radius: 1rem !important;
  background: linear-gradient(to right, #0c035f, #4f46e5) !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  padding: 2rem !important;
  color: white !important;
  /* Prevent any transitions on this element and its children */
  transition: none !important;
  transform: none !important;
  /* Ensure stable layout */
  contain: layout style paint;
  will-change: auto;
}

.dashboard-banner * {
  transition: none !important;
  transform: none !important;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}


