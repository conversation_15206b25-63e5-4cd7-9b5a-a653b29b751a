'use client'

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic'

import PlaceholderPage from '@/components/PlaceholderPage'

export default function ArchivePage() {
  const features = [
    'Archive old records and documents',
    'Automated archiving policies',
    'Search archived content',
    'Restore archived items',
    'Archive compression and optimization',
    'Retention policy management',
    'Archive integrity verification',
    'Export archived data',
    'Archive access controls',
    'Archive audit trails'
  ]

  return (
    <PlaceholderPage
      title="Archive Management"
      description="Manage archived records and historical data"
      iconName="Archive"
      features={features}
    />
  )
}
